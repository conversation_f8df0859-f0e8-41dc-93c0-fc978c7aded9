import { $R } from './config'

/**
 * 小程序登录
 * @param {Object} data 登录数据
 * @returns {Promise}
 */
export function login(data) {
  return $R.post('login.login', data, {
    noAuth: true,
    isJson: true,
    isEncrypt: true, // 启用加密
  })
}

/**
 * 获取验证码
 * @returns {Promise}
 */
export function getCodeImg() {
  return $R.get(
    'login.getCodeImg',
    {},
    {
      noAuth: true,
    },
  )
}

/**
 * 获取租户列表
 * @param {Boolean} isToken 是否需要token
 * @returns {Promise}
 */
export function getTenantList(isToken = false) {
  return $R.get(
    'login.getTenantList',
    {},
    {
      noAuth: !isToken,
    },
  )
}

/**
 * 获取小程序租户列表
 * @param {String} code 小程序code
 * @returns {Promise}
 */
export function getMiniAppTenantList(code) {
  return $R.get(
    'login.getMiniAppTenantList',
    { code },
    {
      noAuth: true,
    },
  )
}

/**
 * 微信登录
 * @param {Object} data 登录数据
 * @returns {Promise}
 */
export function wxLogin(data) {
  return $R.post('login.wxLogin', data, {
    noAuth: true,
    isJson: true,
  })
}
