/**
 * 加密工具函数 - 小程序版本
 * 使用crypto-js进行真正的AES加密
 */
import CryptoJS from 'crypto-js'

/**
 * 随机生成32位的字符串
 * @returns {string}
 */
const generateRandomString = () => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  const charactersLength = characters.length
  for (let i = 0; i < 32; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

/**
 * 随机生成aes 密钥
 * @returns {CryptoJS.lib.WordArray}
 */
export const generateAesKey = () => {
  return CryptoJS.enc.Utf8.parse(generateRandomString())
}

/**
 * 加密base64
 * @param {CryptoJS.lib.WordArray} str
 * @returns {string}
 */
export const encryptBase64 = (str) => {
  return CryptoJS.enc.Base64.stringify(str)
}

/**
 * 解密base64
 * @param {string} str
 * @returns {CryptoJS.lib.WordArray}
 */
export const decryptBase64 = (str) => {
  return CryptoJS.enc.Base64.parse(str)
}

/**
 * 使用密钥对数据进行AES加密
 * @param {string} message
 * @param {CryptoJS.lib.WordArray} aesKey
 * @returns {string}
 */
export const encryptWithAes = (message, aesKey) => {
  try {
    const encrypted = CryptoJS.AES.encrypt(message, aesKey, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    })
    return encrypted.toString()
  } catch (error) {
    console.error('AES encrypt error:', error)
    return message
  }
}

/**
 * 使用密钥对数据进行AES解密
 * @param {string} message
 * @param {CryptoJS.lib.WordArray} aesKey
 * @returns {string}
 */
export const decryptWithAes = (message, aesKey) => {
  try {
    const decrypted = CryptoJS.AES.decrypt(message, aesKey, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    })
    return decrypted.toString(CryptoJS.enc.Utf8)
  } catch (error) {
    console.error('AES decrypt error:', error)
    return message
  }
}

/**
 * 检查是否启用加密
 * @returns {boolean}
 */
export const isEncryptEnabled = () => {
  // 可以通过配置文件或环境变量控制
  return true // 默认启用加密
}
