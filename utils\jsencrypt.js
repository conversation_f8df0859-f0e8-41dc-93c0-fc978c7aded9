/**
 * RSA加密工具 - 小程序版本
 * 使用正确的RSA公钥进行加密
 */
import JSEncrypt from 'jsencrypt'

// 正确的RSA公钥 - 与web端保持一致
const publicKey = `-----BEGIN PUBLIC KEY-----
MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH
nzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==
-----END PUBLIC KEY-----`

// RSA私钥 - 与web端保持一致
const privateKey = `-----BEGIN PRIVATE KEY-----
MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY
7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN
PuH3owIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU
60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+Crhug
AvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJ
i8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrA
WcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4X
q7BpSBwsloE=
-----END PRIVATE KEY-----`

/**
 * RSA加密 - 使用真正的RSA加密
 * @param {string} txt 要加密的文本
 * @returns {string} 加密后的文本
 */
export const encrypt = (txt) => {
  try {
    const encryptor = new JSEncrypt()
    encryptor.setPublicKey(publicKey) // 设置公钥
    const encrypted = encryptor.encrypt(txt) // 对数据进行加密
    return encrypted || txt
  } catch (error) {
    console.error('RSA encrypt error:', error)
    return txt
  }
}

/**
 * RSA解密 - 使用真正的RSA解密
 * @param {string} txt 要解密的文本
 * @returns {string} 解密后的文本
 */
export const decrypt = (txt) => {
  try {
    const encryptor = new JSEncrypt()
    encryptor.setPrivateKey(privateKey) // 设置私钥
    const decrypted = encryptor.decrypt(txt) // 对数据进行解密
    return decrypted || txt
  } catch (error) {
    console.error('RSA decrypt error:', error)
    return txt
  }
}

/**
 * 获取公钥
 * @returns {string}
 */
export const getPublicKey = () => {
  return publicKey
}

/**
 * 获取私钥
 * @returns {string}
 */
export const getPrivateKey = () => {
  return privateKey
}

// 注意：在实际项目中，建议使用以下方案：
// 1. 使用 crypto-js 的小程序版本进行AES加密
// 2. 使用 node-rsa 的小程序兼容版本进行RSA加密
// 3. 或者使用小程序原生的加密API（如果有的话）
// 4. 也可以考虑使用服务端加密，客户端只负责传输
